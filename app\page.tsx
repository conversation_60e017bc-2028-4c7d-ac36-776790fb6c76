"use client";
import { signIn, signOut } from "next-auth/react";
import CurvedTriangle from "../components/CurvedTriangle";

export default function Home() {
  return (
    <div className="">
      <CurvedTriangle />

      <header className="p-4 space-x-4">
        <button onClick={() => signIn("github")}>Sign in with GitHub</button>
        <button onClick={() => signIn("google")}>Sign in with Google</button>
        <button onClick={() => signOut()}>Sign out</button>
      </header>
    </div>
  );
}
