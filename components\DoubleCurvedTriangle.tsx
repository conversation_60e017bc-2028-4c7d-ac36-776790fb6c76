import React from "react";

export default function DoubleCurvedTriangle() {
  return (
    <svg
      className="w-full aspect-[430/275] lg:aspect-[430/200]"
      viewBox="0 0 430 275"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g clip-path="url(#clip0_8_61)">
        <path
          d="M430.356 199.765H-154.999C200.144 154.983 430.356 83.1301 430.356 2.125C430.356 1.41593 430.336 0.707651 430.301 0H430.356V199.765Z"
          fill="var(--primary)"
        />
        <path
          d="M430.356 199.765H-154.999C200.144 154.983 430.356 83.1301 430.356 2.125C430.356 1.41593 430.336 0.707651 430.301 0H430.356V199.765Z"
          fill="var(--secondary)"
        />
        <rect
          x="-154.999"
          y="197.765"
          width="584.858"
          height="83.2353"
          fill="var(--secondary)"
        />
        <rect
          x="-154.999"
          y="197.765"
          width="584.858"
          height="83.2353"
          fill="var(--secondary)"
        />
        <path
          d="M430.357 275.001H-0.0119629C260.97 229.292 430.191 155.965 430.357 73.292V275.001ZM430.357 73.0352C430.356 72.3561 430.341 71.6778 430.317 71H430.357V73.0352Z"
          fill="var(--primary)"
        />
      </g>
      <defs>
        <clipPath id="clip0_8_61">
          <rect width="430" height="275" fill="white" />
        </clipPath>
      </defs>
    </svg>
  );
}
